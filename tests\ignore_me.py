# pyright: basic
from sweeper400.move import (  # type: ignore
    MotorController,
)

mc = MotorController()
out1 = mc.get_hardware_info()
print(out1)


mc.move_relative_mm(
    axis=0,
    distance_mm=5,
    max_speed=5000,
    acceleration=2000,
    deceleration=2000,
    timeout_seconds=60.0,
)

mc.move_relative_mm(
    axis=1,
    distance_mm=-74.253,
    max_speed=5000,
    acceleration=2000,
    deceleration=2000,
    timeout_seconds=60.0,
)

mc.calibrate_all_axis()

out1 = mc.get_current_position_2D()
# out1 = mc.get_axis_status(axis=1)
print(out1)
