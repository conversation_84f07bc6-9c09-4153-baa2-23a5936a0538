"""
测试move_absolute_mm方法的智能参数设置功能

该测试验证智能参数设置的计算逻辑是否正确，包括：
1. 加速度/减速度的分段设置
2. 最大速度的分段设置
3. 超时时间的分段设置

注意：由于未连接电机，此测试主要验证参数计算逻辑
"""

import pytest
from unittest.mock import Mock, patch
from sweeper400.move.controller import MotorController


def test_smart_parameter_calculation():
    """测试智能参数计算逻辑"""

    # 创建一个模拟的控制器实例
    with patch("sweeper400.move.controller.windll") as mock_windll:
        # 模拟DLL加载和API调用
        mock_api = Mock()
        mock_windll.LoadLibrary.return_value = mock_api
        mock_api.MT_Init.return_value = 0
        mock_api.MT_Open_USB.return_value = 0
        mock_api.MT_Check.return_value = 0

        controller = MotorController()

        # 强制设置连接状态为True以便测试参数计算
        controller._is_connected = True

        # 模拟获取当前位置的API调用
        def mock_get_position(axis, pos_ptr):
            pos_ptr.value = 0  # 假设当前位置为0步（0mm）
            return 0

        mock_api.MT_Get_Axis_Software_P_Now.side_effect = mock_get_position

        # 模拟move_relative_mm方法，捕获传入的参数
        captured_params = {}

        def mock_move_relative(
            axis, distance_mm, max_speed, acceleration, deceleration, timeout_seconds
        ):
            captured_params.update(
                {
                    "axis": axis,
                    "distance_mm": distance_mm,
                    "max_speed": max_speed,
                    "acceleration": acceleration,
                    "deceleration": deceleration,
                    "timeout_seconds": timeout_seconds,
                }
            )
            return True

        controller.move_relative_mm = mock_move_relative

        # 测试用例1：小距离运动（1mm）
        controller.move_absolute_mm(0, 1.0)
        assert captured_params["max_speed"] == 1000
        assert captured_params["acceleration"] == 500
        assert captured_params["deceleration"] == 500
        assert captured_params["timeout_seconds"] == 10.0
        print(
            f"1mm运动参数: 速度={captured_params['max_speed']}, 加速度={captured_params['acceleration']}, 超时={captured_params['timeout_seconds']}s"
        )

        # 测试用例2：中等距离运动（15mm，在3-30mm之间）
        controller.move_absolute_mm(0, 15.0)
        # 15mm在3-30mm之间，应该进行线性插值
        # 速度：1000 + (15-3)/(30-3) * (10000-1000) = 1000 + 12/27 * 9000 = 5000
        # 加速度：500 + (15-3)/(30-3) * (5000-500) = 500 + 12/27 * 4500 = 2500
        expected_speed = int(1000 + (15.0 - 3.0) / (30.0 - 3.0) * (10000 - 1000))
        expected_acc = int(500 + (15.0 - 3.0) / (30.0 - 3.0) * (5000 - 500))
        assert captured_params["max_speed"] == expected_speed
        assert captured_params["acceleration"] == expected_acc
        assert captured_params["deceleration"] == expected_acc
        assert captured_params["timeout_seconds"] == 10.0
        print(
            f"15mm运动参数: 速度={captured_params['max_speed']}, 加速度={captured_params['acceleration']}, 超时={captured_params['timeout_seconds']}s"
        )

        # 测试用例3：大距离运动（60mm，在30-120mm之间）
        controller.move_absolute_mm(0, 60.0)
        # 60mm在30-120mm之间
        # 速度：10000 + (60-30)/(120-30) * (20000-10000) = 10000 + 30/90 * 10000 = 13333
        # 加速度：应该是5000（>=30mm）
        # 超时：10 + (60-30)/(120-30) * (15-10) = 10 + 30/90 * 5 = 11.67s
        expected_speed = int(10000 + (60.0 - 30.0) / (120.0 - 30.0) * (20000 - 10000))
        expected_timeout = 10.0 + (60.0 - 30.0) / (120.0 - 30.0) * (15.0 - 10.0)
        assert captured_params["max_speed"] == expected_speed
        assert captured_params["acceleration"] == 5000
        assert captured_params["deceleration"] == 5000
        assert abs(captured_params["timeout_seconds"] - expected_timeout) < 0.1
        print(
            f"60mm运动参数: 速度={captured_params['max_speed']}, 加速度={captured_params['acceleration']}, 超时={captured_params['timeout_seconds']:.1f}s"
        )

        # 测试用例4：超大距离运动（200mm，>120mm）
        controller.move_absolute_mm(0, 200.0)
        # 200mm > 120mm
        # 速度：应该是20000
        # 加速度：应该是5000
        # 超时：15 + (200-120)/(320-120) * (25-15) = 15 + 80/200 * 10 = 19s
        expected_timeout = 15.0 + (200.0 - 120.0) / (320.0 - 120.0) * (25.0 - 15.0)
        assert captured_params["max_speed"] == 20000
        assert captured_params["acceleration"] == 5000
        assert captured_params["deceleration"] == 5000
        assert abs(captured_params["timeout_seconds"] - expected_timeout) < 0.1
        print(
            f"200mm运动参数: 速度={captured_params['max_speed']}, 加速度={captured_params['acceleration']}, 超时={captured_params['timeout_seconds']:.1f}s"
        )

        print("✅ 所有智能参数设置测试通过！")


def test_boundary_conditions():
    """测试边界条件"""

    with patch("sweeper400.move.controller.windll") as mock_windll:
        # 模拟DLL加载和API调用
        mock_api = Mock()
        mock_windll.LoadLibrary.return_value = mock_api
        mock_api.MT_Init.return_value = 0
        mock_api.MT_Open_USB.return_value = 0
        mock_api.MT_Check.return_value = 0

        controller = MotorController()
        controller._is_connected = True

        def mock_get_position(axis, pos_ptr):
            pos_ptr.value = 0
            return 0

        mock_api.MT_Get_Axis_Software_P_Now.side_effect = mock_get_position

        captured_params = {}

        def mock_move_relative(
            axis, distance_mm, max_speed, acceleration, deceleration, timeout_seconds
        ):
            captured_params.update(
                {
                    "max_speed": max_speed,
                    "acceleration": acceleration,
                    "deceleration": deceleration,
                    "timeout_seconds": timeout_seconds,
                }
            )
            return True

        controller.move_relative_mm = mock_move_relative

        # 测试边界值：3mm
        controller.move_absolute_mm(0, 3.0)
        assert captured_params["max_speed"] == 1000
        assert captured_params["acceleration"] == 500
        print(
            f"3mm边界测试: 速度={captured_params['max_speed']}, 加速度={captured_params['acceleration']}"
        )

        # 测试边界值：30mm
        controller.move_absolute_mm(0, 30.0)
        assert captured_params["max_speed"] == 10000
        assert captured_params["acceleration"] == 5000
        assert captured_params["timeout_seconds"] == 10.0
        print(
            f"30mm边界测试: 速度={captured_params['max_speed']}, 加速度={captured_params['acceleration']}, 超时={captured_params['timeout_seconds']}s"
        )

        # 测试边界值：120mm
        controller.move_absolute_mm(0, 120.0)
        assert captured_params["max_speed"] == 20000
        assert captured_params["acceleration"] == 5000
        assert captured_params["timeout_seconds"] == 15.0
        print(
            f"120mm边界测试: 速度={captured_params['max_speed']}, 加速度={captured_params['acceleration']}, 超时={captured_params['timeout_seconds']}s"
        )

        print("✅ 所有边界条件测试通过！")


if __name__ == "__main__":
    test_smart_parameter_calculation()
    test_boundary_conditions()
    print("\n🎉 所有测试完成！智能参数设置功能工作正常。")
