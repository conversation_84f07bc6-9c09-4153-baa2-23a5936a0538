# -*- coding: utf-8 -*-
"""
简单测试智能参数设置的计算逻辑

直接测试参数计算公式，不依赖复杂的mock设置
"""


def calculate_smart_parameters(distance_abs: float) -> tuple[int, int, int, float]:
    """
    根据运动距离计算智能参数
    
    Args:
        distance_abs: 运动距离的绝对值（mm）
        
    Returns:
        tuple: (max_speed, acceleration, deceleration, timeout_seconds)
    """
    # 智能设置加速度/减速度
    if distance_abs <= 3.0:
        acceleration = deceleration = 500
    elif distance_abs >= 30.0:
        acceleration = deceleration = 5000
    else:
        # 3-30mm之间线性插值：从500到5000
        ratio = (distance_abs - 3.0) / (30.0 - 3.0)
        acceleration = deceleration = int(500 + ratio * (5000 - 500))

    # 智能设置最大速度
    if distance_abs <= 3.0:
        max_speed = 1000
    elif distance_abs <= 30.0:
        # 3-30mm之间线性插值：从1000到10000
        ratio = (distance_abs - 3.0) / (30.0 - 3.0)
        max_speed = int(1000 + ratio * (10000 - 1000))
    elif distance_abs <= 120.0:
        # 30-120mm之间线性插值：从10000到20000
        ratio = (distance_abs - 30.0) / (120.0 - 30.0)
        max_speed = int(10000 + ratio * (20000 - 10000))
    else:
        max_speed = 20000

    # 智能设置超时时间
    if distance_abs <= 30.0:
        timeout_seconds = 10.0
    elif distance_abs <= 120.0:
        # 30-120mm之间线性插值：从10s到15s
        ratio = (distance_abs - 30.0) / (120.0 - 30.0)
        timeout_seconds = 10.0 + ratio * (15.0 - 10.0)
    else:
        # 120mm以上：从15s线性延长到320mm的25s，并无限外延
        ratio = (distance_abs - 120.0) / (320.0 - 120.0)
        timeout_seconds = 15.0 + ratio * (25.0 - 15.0)
        # 确保超时时间不会过短
        timeout_seconds = max(timeout_seconds, 15.0)
    
    return max_speed, acceleration, deceleration, timeout_seconds


def test_parameter_calculation():
    """测试参数计算逻辑"""
    
    print("=== 智能参数设置测试 ===\n")
    
    # 测试用例1：小距离运动（1mm）
    max_speed, acc, dec, timeout = calculate_smart_parameters(1.0)
    print(f"1mm运动参数:")
    print(f"  最大速度: {max_speed} Hz/s")
    print(f"  加速度: {acc} Hz/s^2")
    print(f"  减速度: {dec} Hz/s^2")
    print(f"  超时时间: {timeout:.1f}s")
    assert max_speed == 1000
    assert acc == 500
    assert dec == 500
    assert timeout == 10.0
    print("  ✅ 通过\n")
    
    # 测试用例2：边界值（3mm）
    max_speed, acc, dec, timeout = calculate_smart_parameters(3.0)
    print(f"3mm运动参数（边界值）:")
    print(f"  最大速度: {max_speed} Hz/s")
    print(f"  加速度: {acc} Hz/s^2")
    print(f"  减速度: {dec} Hz/s^2")
    print(f"  超时时间: {timeout:.1f}s")
    assert max_speed == 1000
    assert acc == 500
    assert dec == 500
    assert timeout == 10.0
    print("  ✅ 通过\n")
    
    # 测试用例3：中等距离运动（15mm，在3-30mm之间）
    max_speed, acc, dec, timeout = calculate_smart_parameters(15.0)
    print(f"15mm运动参数（线性插值区间）:")
    print(f"  最大速度: {max_speed} Hz/s")
    print(f"  加速度: {acc} Hz/s^2")
    print(f"  减速度: {dec} Hz/s^2")
    print(f"  超时时间: {timeout:.1f}s")
    
    # 验证线性插值计算
    # 速度：1000 + (15-3)/(30-3) * (10000-1000) = 1000 + 12/27 * 9000 = 5000
    expected_speed = int(1000 + (15.0-3.0)/(30.0-3.0) * (10000-1000))
    # 加速度：500 + (15-3)/(30-3) * (5000-500) = 500 + 12/27 * 4500 = 2500
    expected_acc = int(500 + (15.0-3.0)/(30.0-3.0) * (5000-500))
    
    print(f"  预期速度: {expected_speed} Hz/s")
    print(f"  预期加速度: {expected_acc} Hz/s^2")
    
    assert max_speed == expected_speed
    assert acc == expected_acc
    assert dec == expected_acc
    assert timeout == 10.0
    print("  ✅ 通过\n")
    
    # 测试用例4：边界值（30mm）
    max_speed, acc, dec, timeout = calculate_smart_parameters(30.0)
    print(f"30mm运动参数（边界值）:")
    print(f"  最大速度: {max_speed} Hz/s")
    print(f"  加速度: {acc} Hz/s^2")
    print(f"  减速度: {dec} Hz/s^2")
    print(f"  超时时间: {timeout:.1f}s")
    assert max_speed == 10000
    assert acc == 5000
    assert dec == 5000
    assert timeout == 10.0
    print("  ✅ 通过\n")
    
    # 测试用例5：大距离运动（60mm，在30-120mm之间）
    max_speed, acc, dec, timeout = calculate_smart_parameters(60.0)
    print(f"60mm运动参数（第二个线性插值区间）:")
    print(f"  最大速度: {max_speed} Hz/s")
    print(f"  加速度: {acc} Hz/s^2")
    print(f"  减速度: {dec} Hz/s^2")
    print(f"  超时时间: {timeout:.1f}s")
    
    # 验证线性插值计算
    # 速度：10000 + (60-30)/(120-30) * (20000-10000) = 10000 + 30/90 * 10000 = 13333
    expected_speed = int(10000 + (60.0-30.0)/(120.0-30.0) * (20000-10000))
    # 超时：10 + (60-30)/(120-30) * (15-10) = 10 + 30/90 * 5 = 11.67s
    expected_timeout = 10.0 + (60.0-30.0)/(120.0-30.0) * (15.0-10.0)
    
    print(f"  预期速度: {expected_speed} Hz/s")
    print(f"  预期超时: {expected_timeout:.2f}s")
    
    assert max_speed == expected_speed
    assert acc == 5000
    assert dec == 5000
    assert abs(timeout - expected_timeout) < 0.01
    print("  ✅ 通过\n")
    
    # 测试用例6：边界值（120mm）
    max_speed, acc, dec, timeout = calculate_smart_parameters(120.0)
    print(f"120mm运动参数（边界值）:")
    print(f"  最大速度: {max_speed} Hz/s")
    print(f"  加速度: {acc} Hz/s^2")
    print(f"  减速度: {dec} Hz/s^2")
    print(f"  超时时间: {timeout:.1f}s")
    assert max_speed == 20000
    assert acc == 5000
    assert dec == 5000
    assert timeout == 15.0
    print("  ✅ 通过\n")
    
    # 测试用例7：超大距离运动（200mm，>120mm）
    max_speed, acc, dec, timeout = calculate_smart_parameters(200.0)
    print(f"200mm运动参数（超大距离）:")
    print(f"  最大速度: {max_speed} Hz/s")
    print(f"  加速度: {acc} Hz/s^2")
    print(f"  减速度: {dec} Hz/s^2")
    print(f"  超时时间: {timeout:.1f}s")
    
    # 验证超时计算
    # 超时：15 + (200-120)/(320-120) * (25-15) = 15 + 80/200 * 10 = 19s
    expected_timeout = 15.0 + (200.0-120.0)/(320.0-120.0) * (25.0-15.0)
    
    print(f"  预期超时: {expected_timeout:.2f}s")
    
    assert max_speed == 20000
    assert acc == 5000
    assert dec == 5000
    assert abs(timeout - expected_timeout) < 0.01
    print("  ✅ 通过\n")
    
    print("🎉 所有智能参数设置测试通过！")


if __name__ == "__main__":
    test_parameter_calculation()
